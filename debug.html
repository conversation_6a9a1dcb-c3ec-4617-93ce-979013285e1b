<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上传调试页面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        .upload-area {
            border: 2px dashed #ccc;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            margin: 20px 0;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .log {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body class="p-4">
    <h1 class="text-2xl font-bold mb-4">上传功能调试页面</h1>
    
    <div class="mb-4">
        <h2 class="text-lg font-semibold mb-2">环境检查</h2>
        <div id="envCheck" class="log"></div>
    </div>
    
    <div class="mb-4">
        <h2 class="text-lg font-semibold mb-2">上传测试</h2>
        <input type="file" id="fileInput" accept=".xlsx,.xls" style="display: none;">
        <div class="upload-area" id="uploadArea">
            <p>点击这里选择文件</p>
            <p class="text-sm text-gray-500">支持 .xlsx/.xls 格式</p>
        </div>
        <button id="testBtn" class="bg-blue-500 text-white px-4 py-2 rounded">测试按钮</button>
        <div id="status" class="mt-2 text-sm"></div>
    </div>
    
    <div class="mb-4">
        <h2 class="text-lg font-semibold mb-2">调试日志</h2>
        <button id="clearLog" class="bg-red-500 text-white px-2 py-1 rounded text-sm mb-2">清空日志</button>
        <div id="debugLog" class="log"></div>
    </div>

    <script>
        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('debugLog');
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            logDiv.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type}] ${message}`);
        }

        // 环境检查
        function checkEnvironment() {
            const envDiv = document.getElementById('envCheck');
            let result = '';
            
            result += `浏览器: ${navigator.userAgent}\n`;
            result += `页面URL: ${window.location.href}\n`;
            result += `文档状态: ${document.readyState}\n`;
            result += `XLSX库: ${typeof XLSX !== 'undefined' ? '✅ 已加载' : '❌ 未加载'}\n`;
            result += `FileReader: ${typeof FileReader !== 'undefined' ? '✅ 支持' : '❌ 不支持'}\n`;
            
            envDiv.textContent = result;
            log('环境检查完成');
        }

        // 文件处理函数
        function handleFile(file) {
            log(`开始处理文件: ${file.name} (${file.size} bytes)`);
            
            const status = document.getElementById('status');
            status.textContent = `正在处理: ${file.name}`;
            
            if (typeof XLSX === 'undefined') {
                log('XLSX 库未加载！', 'error');
                status.textContent = '错误: XLSX 库未加载';
                return;
            }
            
            const reader = new FileReader();
            
            reader.onload = function(e) {
                log('FileReader.onload 触发');
                try {
                    const data = new Uint8Array(e.target.result);
                    log(`文件读取成功，数据长度: ${data.length}`);
                    
                    if (data.length === 0) {
                        log('文件内容为空', 'error');
                        status.textContent = '错误: 文件为空';
                        return;
                    }
                    
                    log('开始解析 Excel...');
                    const workbook = XLSX.read(data, { type: 'array' });
                    log(`Excel 解析成功！Sheet 数量: ${workbook.SheetNames.length}`, 'success');
                    log(`Sheet 名称: ${workbook.SheetNames.join(', ')}`);
                    
                    // 显示第一个 Sheet 的前几行数据
                    if (workbook.SheetNames.length > 0) {
                        const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
                        const jsonData = XLSX.utils.sheet_to_json(firstSheet, { header: 1 });
                        log(`第一个 Sheet 数据行数: ${jsonData.length}`);
                        if (jsonData.length > 0) {
                            log(`前3行数据: ${JSON.stringify(jsonData.slice(0, 3), null, 2)}`);
                        }
                    }
                    
                    status.textContent = `✅ 处理完成: ${file.name}`;
                    log('文件处理完成', 'success');
                    
                } catch (error) {
                    log(`Excel 解析失败: ${error.message}`, 'error');
                    log(`错误堆栈: ${error.stack}`, 'error');
                    status.textContent = `错误: ${error.message}`;
                }
            };
            
            reader.onerror = function(e) {
                log(`文件读取失败: ${e}`, 'error');
                status.textContent = '错误: 文件读取失败';
            };
            
            log('开始读取文件...');
            reader.readAsArrayBuffer(file);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，开始初始化');
            
            checkEnvironment();
            
            const fileInput = document.getElementById('fileInput');
            const uploadArea = document.getElementById('uploadArea');
            const testBtn = document.getElementById('testBtn');
            const clearLogBtn = document.getElementById('clearLog');
            
            // 点击上传区域
            uploadArea.addEventListener('click', function() {
                log('点击上传区域');
                fileInput.click();
            });
            
            // 测试按钮
            testBtn.addEventListener('click', function() {
                log('点击测试按钮');
                fileInput.click();
            });
            
            // 文件选择
            fileInput.addEventListener('change', function(e) {
                log('文件选择事件触发');
                const file = e.target.files[0];
                if (file) {
                    log(`选择了文件: ${file.name}`);
                    handleFile(file);
                } else {
                    log('没有选择文件');
                }
            });
            
            // 清空日志
            clearLogBtn.addEventListener('click', function() {
                document.getElementById('debugLog').textContent = '';
                log('日志已清空');
            });
            
            log('初始化完成');
        });
    </script>
</body>
</html>
