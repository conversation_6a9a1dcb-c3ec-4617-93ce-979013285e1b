<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>解决方案清单管理工具</title>
    <!-- Tailwind CSS CDN - 使用官方 CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- SheetJS (xlsx) CDN - 使用 BootCDN（七牛云） -->
    <script src="https://cdn.bootcdn.net/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <!-- Font Awesome for icons - 使用 BootCDN（七牛云） -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 自定义样式 */
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .accordion-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        .accordion-content.active {
            max-height: 5000px; /* 增加最大高度以容纳更多内容 */
            transition: max-height 0.5s ease-in-out;
        }
        .sidebar {
            min-height: calc(100vh - 4rem);
        }
        .content-area {
            min-height: calc(100vh - 4rem);
        }
        .file-drop-area {
            border: 2px dashed #cbd5e1;
            transition: all 0.3s ease;
        }
        .file-drop-area.dragover {
            background-color: #f1f5f9;
            border-color: #3b82f6;
        }
        .product-row:hover {
            background-color: #f8fafc;
        }
        .product-row.selected {
            background-color: #dbeafe;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="app-container">
        <!-- Header -->
        <header class="bg-blue-700 text-white p-4 shadow-md">
            <div class="container mx-auto flex justify-between items-center">
                <h1 class="text-2xl font-bold">解决方案清单管理工具</h1>
                <div class="flex space-x-2">
                    <button id="exportChangesBtn" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded-md transition-colors">
                        <i class="fas fa-file-excel mr-2"></i>导出维护记录
                    </button>
                    <button id="exportSolutionBtn" class="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded-md transition-colors">
                        <i class="fas fa-file-export mr-2"></i>导出方案清单
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <div class="app-main flex">
            <!-- Sidebar -->
            <aside class="sidebar w-80 bg-white shadow-md p-4">
                <!-- File Upload Section -->
                <div class="mb-6">
                    <h2 class="text-lg font-semibold mb-3">数据导入</h2>
                    <label for="fileInput" class="file-drop-area p-6 rounded-lg text-center cursor-pointer block w-full" id="fileDropArea" tabindex="0" aria-label="上传Excel文件，支持 .xls/.xlsx 格式">
                        <i class="fas fa-cloud-upload-alt text-3xl text-gray-400 mb-2"></i>
                        <p class="text-gray-600">拖拽文件到此处或点击上传</p>
                        <p class="text-sm text-gray-500 mt-1">支持 .xls/.xlsx 格式</p>
                    </label>
                    <input type="file" id="fileInput" class="hidden" accept=".xlsx,.xls">
                        <div id="uploadStatus" class="text-sm text-gray-500 mt-2"></div>
                        <button id="chooseFileBtn" class="mt-3 w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-md transition-colors">点击选择文件</button>
                </div>

                <!-- Solution List -->
                <div class="mb-6">
                    <h2 class="text-lg font-semibold mb-3">方案列表</h2>
                    <div id="solutionList" class="space-y-2">
                        <div class="text-gray-500 text-center py-4">暂无方案数据</div>
                    </div>
                </div>

                <!-- Product Filter -->
                <div class="mb-6">
                    <h2 class="text-lg font-semibold mb-3">产品筛选</h2>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">生命周期</label>
                            <select id="lifecycleFilter" class="w-full p-2 border border-gray-300 rounded-md">
                                <option value="">全部</option>
                                <option value="开发">开发</option>
                                <option value="量产">量产</option>
                                <option value="即将停售">即将停售</option>
                                <option value="废弃">废弃</option>
                                <option value="工程样机">工程样机</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">排序方式</label>
                            <select id="sortOrder" class="w-full p-2 border border-gray-300 rounded-md">
                                <option value="default">默认排序</option>
                                <option value="name-asc">产品名称 A-Z</option>
                                <option value="name-desc">产品名称 Z-A</option>
                            </select>
                        </div>
                        <button id="applyFilterBtn" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-md transition-colors">
                            应用筛选
                        </button>
                    </div>
                </div>
            </aside>

            <!-- Content Area -->
            <main class="content-area flex-1 p-6">
                <!-- Solution Viewer -->
                <div id="solutionViewer" class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <div class="text-center text-gray-500 py-12">
                        <i class="fas fa-folder-open text-5xl mb-4"></i>
                        <p class="text-xl">请选择一个方案查看详情</p>
                    </div>
                </div>

                <!-- Product Maintenance -->
                <div id="productMaintenance" class="bg-white rounded-lg shadow-md p-6 hidden">
                    <h2 class="text-xl font-semibold mb-4">产品数据维护</h2>
                    <div id="productActions" class="flex space-x-2 mb-4">
                        <button id="replaceProductBtn" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-md transition-colors disabled:opacity-50" disabled>
                            <i class="fas fa-exchange-alt mr-2"></i>替换
                        </button>
                        <button id="deleteProductBtn" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md transition-colors disabled:opacity-50" disabled>
                            <i class="fas fa-trash-alt mr-2"></i>删除
                        </button>
                        <button id="insertAboveBtn" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md transition-colors disabled:opacity-50" disabled>
                            <i class="fas fa-arrow-up mr-2"></i>向上插入
                        </button>
                        <button id="insertBelowBtn" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md transition-colors disabled:opacity-50" disabled>
                            <i class="fas fa-arrow-down mr-2"></i>向下插入
                        </button>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Replace Product Modal -->
    <div id="replaceModal" class="modal">
        <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
            <h3 class="text-xl font-semibold mb-4">替换产品</h3>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">当前产品编号</label>
                    <input type="text" id="currentProductNumber" class="w-full p-2 border border-gray-300 rounded-md bg-gray-100" readonly>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">新产品编号</label>
                    <input type="text" id="newProductNumber" class="w-full p-2 border border-gray-300 rounded-md" placeholder="请输入新产品编号">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">产品描述</label>
                    <input type="text" id="newProductDescription" class="w-full p-2 border border-gray-300 rounded-md" placeholder="请输入产品描述">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">生命周期</label>
                    <select id="newProductLifecycle" class="w-full p-2 border border-gray-300 rounded-md">
                        <option value="开发">开发</option>
                        <option value="量产">量产</option>
                        <option value="即将停售">即将停售</option>
                        <option value="废弃">废弃</option>
                        <option value="工程样机">工程样机</option>
                    </select>
                </div>
                <div class="mt-4">
                    <label class="flex items-center">
                        <input type="checkbox" id="batchReplaceCheckbox" class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-600">批量替换所有方案中的相同物料</span>
                    </label>
                </div>
            </div>
            <div class="flex justify-end space-x-2 mt-6">
                <button id="cancelReplaceBtn" class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100 transition-colors">取消</button>
                <button id="confirmReplaceBtn" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">确认替换</button>
            </div>
        </div>
    </div>

    <!-- Insert Product Modal -->
    <div id="insertModal" class="modal">
        <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
            <h3 class="text-xl font-semibold mb-4">插入产品</h3>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">产品编号</label>
                    <input type="text" id="insertProductNumber" class="w-full p-2 border border-gray-300 rounded-md" placeholder="请输入产品编号">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">产品描述</label>
                    <input type="text" id="insertProductDescription" class="w-full p-2 border border-gray-300 rounded-md" placeholder="请输入产品描述">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">生命周期</label>
                    <select id="insertProductLifecycle" class="w-full p-2 border border-gray-300 rounded-md">
                        <option value="开发">开发</option>
                        <option value="量产">量产</option>
                        <option value="即将停售">即将停售</option>
                        <option value="废弃">废弃</option>
                        <option value="工程样机">工程样机</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                    <input type="text" id="insertProductNote" class="w-full p-2 border border-gray-300 rounded-md" placeholder="请输入备注">
                </div>
            </div>
            <div class="flex justify-end space-x-2 mt-6">
                <button id="cancelInsertBtn" class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100 transition-colors">取消</button>
                <button id="confirmInsertBtn" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">确认插入</button>
            </div>
        </div>
    </div>

    <!-- JavaScript Code -->
    <script>
        console.log("脚本块开始执行");
        // 应用主控制器
        class SolutionManagerApp {
            constructor() {
                console.log("SolutionManagerApp constructor 开始");
                this.data = {
                    solutions: [],
                    productLifecycle: {},
                    changeLog: []
                };
                this.currentSolution = null;
                this.selectedProduct = null;
                this.insertPosition = null;

                this.init();
                console.log("SolutionManagerApp constructor 结束");
            }

            init() {
                console.log("init() 方法开始");
                // 从localStorage加载数据
                this.loadDataFromStorage();

                // 初始化事件监听器
                this.initEventListeners();

                // 渲染方案列表
                this.renderSolutionList();
                console.log("init() 方法结束");
            }

            // 从localStorage加载数据
            loadDataFromStorage() {
                const savedData = localStorage.getItem('solutionManagerData');
                if (savedData) {
                    this.data = JSON.parse(savedData);
                }
            }

            // 保存数据到localStorage
            saveDataToStorage() {
                localStorage.setItem('solutionManagerData', JSON.stringify(this.data));
            }

            // 初始化事件监听器
            initEventListeners() {
                // 文件上传
                const fileDropArea = document.getElementById('fileDropArea');
                const fileInput = document.getElementById('fileInput');

                // 显式代理点击与键盘触发，兼容部分浏览器对 label(for) 的限制
                const openChooser = () => {
                    try {
                        console.log('尝试打开文件选择器');
                        fileInput.value = '';
                        fileInput.click();
                        console.log('文件选择器已触发');
                    } catch (e) {
                        console.warn('打开文件选择器失败:', e);
                        const status = document.getElementById('uploadStatus');
                        if (status) status.textContent = '无法打开文件选择器';
                    }
                };

                // 多种触发方式
                fileDropArea.addEventListener('click', (e) => {
                    console.log('fileDropArea 被点击');
                    e.preventDefault();
                    openChooser();
                });
                fileDropArea.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                        console.log('fileDropArea 键盘触发');
                        e.preventDefault();
                        openChooser();
                    }
                });

                // 额外的按钮触发
                const chooseBtn = document.getElementById('chooseFileBtn');
                if (chooseBtn) {
                    chooseBtn.addEventListener('click', (e) => {
                        console.log('chooseFileBtn 被点击');
                        e.preventDefault();
                        openChooser();
                    });
                }

                fileInput.addEventListener('change', (e) => {
                    console.log('fileInput change 事件触发，files:', e.target.files);
                    const f = e.target.files && e.target.files[0];
                    const status = document.getElementById('uploadStatus');
                    if (!f) {
                        console.log('未选择文件');
                        if (status) status.textContent = '未选择文件';
                        return;
                    }
                    console.log('选择的文件:', f.name, '大小:', f.size);
                    if (status) status.textContent = `正在读取：${f.name}`;
                    this.handleFileUpload(f);
                });

                // 拖拽上传
                fileDropArea.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    fileDropArea.classList.add('dragover');
                });

                fileDropArea.addEventListener('dragleave', () => {
                    fileDropArea.classList.remove('dragover');
                });

                fileDropArea.addEventListener('drop', (e) => {
                    e.preventDefault();
                    fileDropArea.classList.remove('dragover');
                    if (e.dataTransfer.files.length > 0) {
                        this.handleFileUpload(e.dataTransfer.files[0]);
                    }
                });

                // 导出按钮
                document.getElementById('exportChangesBtn').addEventListener('click', () => this.exportChangeLog());
                document.getElementById('exportSolutionBtn').addEventListener('click', () => this.exportCurrentSolution());

                // 筛选按钮
                document.getElementById('applyFilterBtn').addEventListener('click', () => this.applyProductFilter());

                // 产品操作按钮
                document.getElementById('replaceProductBtn').addEventListener('click', () => this.openReplaceModal());
                document.getElementById('deleteProductBtn').addEventListener('click', () => this.deleteProduct());
                document.getElementById('insertAboveBtn').addEventListener('click', () => this.openInsertModal('above'));
                document.getElementById('insertBelowBtn').addEventListener('click', () => this.openInsertModal('below'));

                // 模态框按钮
                document.getElementById('cancelReplaceBtn').addEventListener('click', () => this.closeReplaceModal());
                document.getElementById('confirmReplaceBtn').addEventListener('click', () => this.confirmReplace());
                document.getElementById('cancelInsertBtn').addEventListener('click', () => this.closeInsertModal());
                document.getElementById('confirmInsertBtn').addEventListener('click', () => this.confirmInsert());
            }

            // 处理文件上传
            handleFileUpload(file) {
                console.log('=== handleFileUpload 开始 ===');
                console.log('文件对象:', file);
                console.log('文件名:', file ? file.name : '无文件');
                console.log('文件大小:', file ? file.size : '无文件');
                console.log('文件类型:', file ? file.type : '无文件');

                const status = document.getElementById('uploadStatus');

                if (!file) {
                    console.log('❌ 没有选择文件');
                    if (status) status.textContent = '❌ 没有选择文件';
                    return;
                }

                if (status) status.textContent = `📖 正在读取：${file.name}`;
                console.log('开始读取文件...');
                const reader = new FileReader();
                reader.onload = (e) => {
                    console.log('FileReader.onload 被触发');
                    try {
                        const data = new Uint8Array(e.target.result);
                        console.log('读取数据成功，数据长度:', data.length);
                        if (data.length === 0) {
                            this.showNotification('文件内容为空，请检查文件是否损坏', 'error');
                            const status = document.getElementById('uploadStatus');
                            if (status) status.textContent = '读取失败：文件为空';
                            return;
                        }
                        if (typeof XLSX === 'undefined') {
                            console.error('XLSX 未加载');
                            this.showNotification('依赖库未加载（XLSX）。请检查网络或使用本地兜底。', 'error');
                            const status = document.getElementById('uploadStatus');
                            if (status) status.textContent = '读取失败：XLSX 未加载';
                            return;
                        }
                        const workbook = XLSX.read(data, { type: 'array' });
                        console.log('Excel解析成功，Sheet数量:', workbook.SheetNames.length);

                        // 检查是否存在“政府退市查询”文件
                        if (file.name.includes('政府退市查询')) {
                            console.log('识别为产品生命周期文件');
                            this.processProductLifecycleFile(workbook);
                            const status = document.getElementById('uploadStatus');
                            if (status) status.textContent = '已导入：产品生命周期数据';
                        } else { // 否则视为解决方案文件
                            console.log('识别为解决方案文件');
                            this.processSolutionFile(workbook, file.name);
                            const status = document.getElementById('uploadStatus');
                            if (status) status.textContent = `已导入方案：${file.name.replace(/\.xlsx$/i,'')}`;
                        }
                    } catch (error) {
                        console.error('文件解析错误:', error);
                        this.showNotification('文件解析失败，请确保文件格式正确', 'error');
                        const status = document.getElementById('uploadStatus');
                        if (status) status.textContent = '读取失败：解析错误';
                    }
                };
                reader.onerror = (e) => {
                    console.error('FileReader读取错误:', e);
                    this.showNotification('文件读取失败', 'error');
                };
                console.log('开始读取文件');
                reader.readAsArrayBuffer(file);
            }

            // 表头映射：在前 200 行内搜索包含“级别”的表头行并定位各列
            getHeaderMapping(rows) {
                const norm = (s) => (s == null ? '' : String(s)).trim();
                const findIdx = (headerRow, candidates) => {
                    const idx = headerRow.findIndex(cell => {
                        const t = norm(cell);
                        return candidates.some(c => t === c);
                    });
                    return idx >= 0 ? idx : null;
                };
                const findIdxFuzzy = (headerRow, includes, excludes = []) => {
                    const idx = headerRow.findIndex(cell => {
                        const t = norm(cell);
                        if (!t) return false;
                        if (excludes.some(ex => t.includes(ex))) return false;
                        return includes.some(inc => t.includes(inc));
                    });
                    return idx >= 0 ? idx : null;
                };
                const isProbableBomHeader = (row) => {
                    const t = row.map(norm);
                    const hasLevel = t.includes('级别');
                    const hasPartNo = t.includes('物件编号') || t.includes('产品编号') || t.includes('物料编号') || t.includes('料号');
                    const hasDesc = t.includes('物件描述') || t.includes('描述/名称') || t.includes('产品描述') || t.includes('物件名称');
                    return hasLevel && (hasPartNo || hasDesc);
                };
                for (let i = 0; i < Math.min(200, rows.length); i++) {
                    const row = rows[i];
                    if (!Array.isArray(row)) continue;
                    if (!isProbableBomHeader(row)) continue;

                    const cols = {
                        level: findIdx(row, ['级别']),
                        partNumber: findIdx(row, ['物件编号', '产品编号', '物料编号', '料号']),
                        description: findIdx(row, ['物件描述', '描述/名称', '物件名称', '产品描述']),
                        partType: findIdx(row, ['物件类型']),
                        quantity: findIdx(row, ['数量']),
                        lifecycle: findIdx(row, ['生命周期', '生命周期阶段', '物件的生命周期阶段']),
                        note: findIdx(row, ['备注'])
                    };
                    // 宽松匹配兜底（避免误选“查找编号/位置号”等）
                    if (cols.partNumber == null) cols.partNumber = findIdxFuzzy(row, ['物件编号', '产品编号', '料号']);
                    if (cols.description == null) cols.description = findIdxFuzzy(row, ['物件描述', '产品描述', '描述', '名称']);
                    if (cols.lifecycle == null) cols.lifecycle = findIdxFuzzy(row, ['生命周期']);
                    if (cols.note == null) cols.note = findIdxFuzzy(row, ['备注']);

                    console.log('检测到BOM表头行索引:', i, '表头内容:', row, '映射结果:', cols);

                    if (cols.level == null || (cols.partNumber == null && cols.description == null)) continue;
                    return { startIndex: i + 1, cols };
                }
                return null;
            }

            // 处理解决方案文件
            processSolutionFile(workbook, fileName) {
                const solutionId = fileName.replace(/\.xlsx$/i, '');
                // 假设第一个sheet是BOM
                const bomSheetName = workbook.SheetNames[0];
                const bomWorksheet = workbook.Sheets[bomSheetName];
                const bomJson = XLSX.utils.sheet_to_json(bomWorksheet, { header: 1 });

                // 提取方案名称（尽力从前几行第2列找包含【 的文字，否则用文件名）
                let solutionName = `方案 ${solutionId}`;
                for (let i = 0; i < Math.min(30, bomJson.length); i++) {
                    const row = bomJson[i];
                    if (Array.isArray(row) && row.length > 1 && typeof row[1] === 'string' && row[1].includes('【')) {
                        solutionName = row[1];
                        break;
                    }
                }

                // 基于表头动态定位列
                const mapping = this.getHeaderMapping(bomJson);
                if (!mapping) {
                    this.showNotification(`文件 ${fileName} 中未找到有效的BOM表头（缺少“级别”等列）`, 'error');
                    return;
                }

                const hierarchy = this.buildHierarchy(bomJson.slice(mapping.startIndex), mapping.cols);

                const solution = {
                    id: solutionId,
                    name: solutionName,
                    hierarchy: hierarchy,
                };

                const existingIndex = this.data.solutions.findIndex(s => s.id === solutionId);
                if (existingIndex > -1) {
                    this.data.solutions[existingIndex] = solution;
                } else {
                    this.data.solutions.push(solution);
                }

                this.saveDataToStorage();
                this.renderSolutionList();
                this.showNotification(`成功导入方案: ${solutionName}`, 'success');
            }

            // 构建层级数据（基于表头映射）
            buildHierarchy(dataRows, cols) {
                const toStr = (v) => v == null ? '' : String(v).trim();
                const isBoolWord = (s) => s === 'true' || s === 'false';
                const looksLikePartNo = (s) => /^(X\d{2}\.|\d+\.)/.test(s) || /\./.test(s);
                const root = { children: [] };
                const path = [root];

                for (const row of dataRows) {
                    if (!Array.isArray(row)) continue;
                    const levelCell = cols.level != null ? row[cols.level] : undefined;
                    const level = parseInt(levelCell, 10);
                    if (isNaN(level) || level <= 0) continue;

                    let partNumber = toStr(cols.partNumber != null ? row[cols.partNumber] : '');
                    let description = toStr(cols.description != null ? row[cols.description] : '');

                    // 回退：若编号/描述异常（为空或布尔字样），尝试在整行中自动探测
                    if (!partNumber || isBoolWord(partNumber) || !looksLikePartNo(partNumber)) {
                        for (let i = 0; i < row.length; i++) {
                            const t = toStr(row[i]);
                            if (looksLikePartNo(t)) { partNumber = t; break; }
                        }
                    }
                    if (!description || isBoolWord(description)) {
                        // 优先尝试紧邻编号右侧2列内找到较长文本
                        const tryNeighbors = () => {
                            const candidates = [];
                            const pnIdx = cols.partNumber != null ? cols.partNumber : -1;
                            for (let off = 1; off <= 2; off++) {
                                const idx = pnIdx + off;
                                if (idx >= 0 && idx < row.length) {
                                    const t = toStr(row[idx]);
                                    if (t && !looksLikePartNo(t) && !isBoolWord(t) && t.length >= 2) candidates.push(t);
                                }
                            }
                            return candidates[0] || '';
                        };
                        description = tryNeighbors();
                        if (!description) {
                            // 全行兜底：找第一个非布尔、非编号的较长文本
                            for (let i = 0; i < row.length; i++) {
                                const t = toStr(row[i]);
                                if (t && !looksLikePartNo(t) && !isBoolWord(t) && t.length >= 2) { description = t; break; }
                            }
                        }
                    }

                    const item = {
                        level,
                        partNumber,
                        description,
                        partType: toStr(cols.partType != null ? row[cols.partType] : ''),
                        quantity: toStr(cols.quantity != null ? row[cols.quantity] : ''),
                        lifecycle: toStr(cols.lifecycle != null ? row[cols.lifecycle] : ''),
                        note: toStr(cols.note != null ? row[cols.note] : ''),
                        children: []
                    };

                    // 确保路径正确
                    while (level < path.length) path.pop();
                    path[path.length - 1].children.push(item);
                    path.push(item);
                }
                return root.children;
            }

            // 处理产品生命周期文件
            processProductLifecycleFile(workbook) {
                const sheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[sheetName];
                const jsonData = XLSX.utils.sheet_to_json(worksheet);

                jsonData.forEach(row => {
                    const productId = row['产品编号'];
                    if (productId) {
                        this.data.productLifecycle[productId] = {
                            lifecycle: row['生命周期'],
                            description: row['产品描述']
                        };
                    }
                });

                this.saveDataToStorage();
                this.showNotification('产品生命周期数据已更新', 'success');

                // 如果当前有选中的方案，刷新视图以显示最新生命周期
                if (this.currentSolution) {
                    this.renderSolutionViewer();
                }
            }

            // 渲染方案列表
            renderSolutionList() {
                const listEl = document.getElementById('solutionList');
                if (this.data.solutions.length === 0) {
                    listEl.innerHTML = `<div class="text-gray-500 text-center py-4">暂无方案数据</div>`;
                    return;
                }

                listEl.innerHTML = this.data.solutions.map(s => `
                    <div class="p-3 bg-gray-50 rounded-md hover:bg-gray-100 cursor-pointer transition-colors" data-solution-id="${s.id}">
                        <div class="font-medium">${s.id}</div>
                        <div class="text-sm text-gray-600 truncate">${s.name}</div>
                    </div>
                `).join('');

                listEl.querySelectorAll('[data-solution-id]').forEach(el => {
                    el.addEventListener('click', () => {
                        const solution = this.data.solutions.find(s => s.id === el.dataset.solutionId);
                        this.selectSolution(solution);
                    });
                });
            }

            // 选择方案
            selectSolution(solution) {
                this.currentSolution = solution;
                this.renderSolutionViewer();
                document.getElementById('productMaintenance').classList.remove('hidden');

                document.querySelectorAll('#solutionList > div').forEach(el => {
                    el.classList.toggle('bg-blue-100', el.dataset.solutionId === solution.id);
                });
            }

            // 渲染方案详情
            renderSolutionViewer(filter = {}) {
                const viewerEl = document.getElementById('solutionViewer');
                if (!this.currentSolution) {
                    viewerEl.innerHTML = `<div class="text-center text-gray-500 py-12"><i class="fas fa-folder-open text-5xl mb-4"></i><p class="text-xl">请选择一个方案查看详情</p></div>`;
                    return;
                }

                const { hierarchy } = this.currentSolution;
                const nodes = Array.isArray(hierarchy) ? hierarchy : [];

                // 更健壮地收集“级别2”节点：
                // 1) 若顶层存在 level===1，则采集所有 level1 的直接子节点中 level===2 的项
                // 2) 若顶层最小等级为2，则直接将顶层的 level===2 作为页签
                // 3) 若仍为空，回退到旧逻辑：取首个节点的 children
                let level2Nodes = [];
                if (nodes.length > 0) {
                    const minLevel = Math.min(...nodes.map(n => (typeof n.level === 'number' ? n.level : Infinity)));
                    if (minLevel === 1) {
                        nodes
                            .filter(n => n.level === 1 && Array.isArray(n.children))
                            .forEach(l1 => {
                                l1.children
                                    .filter(c => c.level === 2)
                                    .forEach(c => level2Nodes.push(c));
                            });
                    } else if (minLevel === 2) {
                        level2Nodes = nodes.filter(n => n.level === 2);
                    }
                }
                if (level2Nodes.length === 0 && nodes.length > 0 && Array.isArray(nodes[0].children)) {
                    level2Nodes = nodes[0].children;
                }

                let tabsHtml = '<div class="flex border-b border-gray-200 mb-4">';
                let contentHtml = '<div class="tab-content-container">';

                level2Nodes.forEach((node, index) => {
                    const isActive = index === 0;
                    tabsHtml += `<button class="tab-btn px-4 py-2 font-medium text-sm ${isActive ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'}" data-tab="tab-${index}">${node.description}</button>`;
                    contentHtml += `<div id="tab-${index}" class="tab-content ${isActive ? 'active' : ''}">${this.renderLevel3And4(node.children || [], filter)}</div>`;
                });

                tabsHtml += '</div>';
                contentHtml += '</div>';

                viewerEl.innerHTML = `
                    <div class="mb-6">
                        <h2 class="text-2xl font-bold">${this.currentSolution.id}</h2>
                        <p class="text-gray-600">${this.currentSolution.name}</p>
                    </div>
                    ${tabsHtml}
                    ${contentHtml}
                `;
                this.addTabEventListeners();
                this.addProductRowEventListeners();
            }

            // 渲染级别3和4的内容
            renderLevel3And4(level3Nodes, filter) {
                let html = '';
                level3Nodes.forEach(level3Node => {
                    html += `
                        <div class="accordion-item mb-2">
                            <button class="accordion-header w-full text-left p-3 bg-gray-100 hover:bg-gray-200 rounded-md font-semibold">
                                ${level3Node.description}
                            </button>
                            <div class="accordion-content bg-white p-2 rounded-b-md">
                                ${this.renderProductTable(level3Node.children, filter)}
                            </div>
                        </div>
                    `;
                });
                return html;
            }

            // 渲染产品表格
            renderProductTable(products, filter) {
                let filteredProducts = [...products];

                // 应用生命周期筛选
                if (filter.lifecycle) {
                    filteredProducts = filteredProducts.filter(p => {
                        const lifecycle = (this.data.productLifecycle[p.partNumber]?.lifecycle) || p.lifecycle;
                        return lifecycle === filter.lifecycle;
                    });
                }

                // 应用排序
                if (filter.sortOrder === 'name-asc') {
                    filteredProducts.sort((a, b) => a.description.localeCompare(b.description));
                } else if (filter.sortOrder === 'name-desc') {
                    filteredProducts.sort((a, b) => b.description.localeCompare(a.description));
                }

                if (filteredProducts.length === 0) {
                    return '<div class="text-center text-gray-500 p-4">无匹配产品</div>';
                }

                const header = `
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">产品编号</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">产品描述</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">生命周期</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备注</th>
                        </tr>
                    </thead>`;

                const body = filteredProducts.map(p => this.renderProductRow(p)).join('');

                return `<table class="min-w-full divide-y divide-gray-200">${header}<tbody class="bg-white divide-y divide-gray-200">${body}</tbody></table>`;
            }

            // 渲染单行产品
            renderProductRow(product) {
                const lifecycleInfo = this.data.productLifecycle[product.partNumber] || {};
                const description = lifecycleInfo.description || product.description;
                const lifecycle = lifecycleInfo.lifecycle || product.lifecycle || '未知';

                return `
                    <tr class="product-row" data-product-id="${product.partNumber}">
                        <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">${product.partNumber}</td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-600">${description}</td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-600">${lifecycle}</td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-600">${product.note}</td>
                    </tr>
                `;
            }

            // 添加页签事件监听
            addTabEventListeners() {
                // Tabs 切换
                document.querySelectorAll('.tab-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const target = e.currentTarget;
                        const tabId = target.dataset.tab;
                        document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('text-blue-600', 'border-b-2', 'border-blue-600'));
                        target.classList.add('text-blue-600', 'border-b-2', 'border-blue-600');
                        document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                        const tabEl = document.getElementById(tabId);
                        if (tabEl) tabEl.classList.add('active');
                    });
                });

                // 手风琴展开/收起
                document.querySelectorAll('.accordion-header').forEach(header => {
                    header.addEventListener('click', (e) => {
                        const content = e.currentTarget.nextElementSibling;
                        if (!content) return;
                        const isOpen = content.classList.toggle('active');
                        if (isOpen) {
                            content.style.maxHeight = content.scrollHeight + 'px';
                        } else {
                            content.style.maxHeight = null;
                        }
                    });
                });
            }

            // 添加产品行事件监听
            addProductRowEventListeners() {
                document.querySelectorAll('.product-row').forEach(row => {
                    row.addEventListener('click', () => {
                        if (this.selectedProduct && this.selectedProduct.partNumber === row.dataset.productId) {
                            row.classList.remove('selected');
                            this.selectedProduct = null;
                            this.updateActionButtonsState();
                        } else {
                            document.querySelectorAll('.product-row').forEach(r => r.classList.remove('selected'));
                            row.classList.add('selected');
                            this.selectedProduct = this.findProduct(this.currentSolution.hierarchy, row.dataset.productId);
                            this.updateActionButtonsState();
                        }
                    });
                });
            }

            // 更新操作按钮状态
            updateActionButtonsState() {
                const hasSelection = !!this.selectedProduct;
                document.getElementById('replaceProductBtn').disabled = !hasSelection;
                document.getElementById('deleteProductBtn').disabled = !hasSelection;
                document.getElementById('insertAboveBtn').disabled = !hasSelection;
                document.getElementById('insertBelowBtn').disabled = !hasSelection;
            }

            // 查找产品
            findProduct(nodes, productId) {
                for (const node of nodes) {
                    if (node.partNumber === productId) return node;
                    if (node.children && node.children.length > 0) {
                        const found = this.findProduct(node.children, productId);
                        if (found) return found;
                    }
                }
                return null;
            }

            // 应用筛选
            applyProductFilter() {
                if (!this.currentSolution) {
                    this.showNotification('请先选择一个方案', 'warning');
                    return;
                }
                const lifecycle = document.getElementById('lifecycleFilter').value;
                const sortOrder = document.getElementById('sortOrder').value;
                this.renderSolutionViewer({ lifecycle, sortOrder });
            }

            // 打开替换模态框
            openReplaceModal() {
                if (!this.selectedProduct) return;
                document.getElementById('currentProductNumber').value = this.selectedProduct.partNumber;
                document.getElementById('replaceModal').classList.add('active');
            }

            // 关闭替换模态框
            closeReplaceModal() {
                document.getElementById('replaceModal').classList.remove('active');
                document.getElementById('newProductNumber').value = '';
                document.getElementById('newProductDescription').value = '';
            }

            // 确认替换
            confirmReplace() {
                const oldProductNumber = this.selectedProduct.partNumber;
                const newProductNumber = document.getElementById('newProductNumber').value.trim();
                const newProductDescription = document.getElementById('newProductDescription').value.trim();
                const newProductLifecycle = document.getElementById('newProductLifecycle').value;
                const isBatch = document.getElementById('batchReplaceCheckbox').checked;

                if (!newProductNumber) {
                    this.showNotification('新产品编号不能为空', 'error');
                    return;
                }

                const updateProduct = (nodes) => {
                    nodes.forEach(node => {
                        if (node.partNumber === oldProductNumber) {
                            node.partNumber = newProductNumber;
                            node.description = newProductDescription || `描述 for ${newProductNumber}`;
                            node.lifecycle = newProductLifecycle;
                        }
                        if (node.children) updateProduct(node.children);
                    });
                };

                if (isBatch) {
                    this.data.solutions.forEach(solution => updateProduct(solution.hierarchy));
                } else {
                    updateProduct(this.currentSolution.hierarchy);
                }

                this.logChange({
                    type: '替换',
                    solutionId: this.currentSolution.id,
                    oldProductNumber,
                    newProductNumber,
                    newProductDescription,
                    newProductLifecycle
                });

                this.saveDataToStorage();
                this.renderSolutionViewer();
                this.closeReplaceModal();
                this.showNotification('产品替换成功', 'success');
            }

            // 打开插入模态框
            openInsertModal(position) {
                if (!this.selectedProduct) return;
                this.insertPosition = position;
                document.getElementById('insertModal').classList.add('active');
            }

            // 关闭插入模态框
            closeInsertModal() {
                document.getElementById('insertModal').classList.remove('active');
                document.getElementById('insertProductNumber').value = '';
                document.getElementById('insertProductDescription').value = '';
                document.getElementById('insertProductNote').value = '';
                this.insertPosition = null;
            }

            // 确认插入
            confirmInsert() {
                const newProduct = {
                    partNumber: document.getElementById('insertProductNumber').value.trim(),
                    description: document.getElementById('insertProductDescription').value.trim(),
                    lifecycle: document.getElementById('insertProductLifecycle').value,
                    note: document.getElementById('insertProductNote').value.trim(),
                    level: this.selectedProduct.level,
                    children: []
                };

                if (!newProduct.partNumber) {
                    this.showNotification('产品编号不能为空', 'error');
                    return;
                }

                const findAndInsert = (nodes) => {
                    for (let i = 0; i < nodes.length; i++) {
                        if (nodes[i].partNumber === this.selectedProduct.partNumber) {
                            const insertIndex = this.insertPosition === 'above' ? i : i + 1;
                            nodes.splice(insertIndex, 0, newProduct);
                            return true;
                        }
                        if (nodes[i].children && findAndInsert(nodes[i].children)) {
                            return true;
                        }
                    }
                    return false;
                };

                findAndInsert(this.currentSolution.hierarchy);

                this.logChange({
                    type: '新增',
                    solutionId: this.currentSolution.id,
                    product: newProduct
                });

                this.saveDataToStorage();
                this.renderSolutionViewer();
                this.closeInsertModal();
                this.showNotification('产品插入成功', 'success');
            }

            // 删除产品
            deleteProduct() {
                if (!this.selectedProduct || !confirm(`确定要删除产品 ${this.selectedProduct.partNumber} 吗？`)) {
                    return;
                }

                const findAndDelete = (nodes) => {
                    for (let i = 0; i < nodes.length; i++) {
                        if (nodes[i].partNumber === this.selectedProduct.partNumber) {
                            nodes.splice(i, 1);
                            return true;
                        }
                        if (nodes[i].children && findAndDelete(nodes[i].children)) {
                            return true;
                        }
                    }
                    return false;
                };

                findAndDelete(this.currentSolution.hierarchy);

                this.logChange({
                    type: '删除',
                    solutionId: this.currentSolution.id,
                    product: this.selectedProduct
                });

                this.selectedProduct = null;
                this.updateActionButtonsState();
                this.saveDataToStorage();
                this.renderSolutionViewer();
                this.showNotification('产品删除成功', 'success');
            }

            // 记录变更
            logChange(change) {
                this.data.changeLog.push({
                    ...change,
                    timestamp: new Date().toISOString()
                });
                this.saveDataToStorage();
            }

            // 导出变更记录
            exportChangeLog() {
                if (this.data.changeLog.length === 0) {
                    this.showNotification('没有可导出的变更记录', 'info');
                    return;
                }

                const exportData = this.data.changeLog.map(log => {
                    const solution = this.data.solutions.find(s => s.id === log.solutionId);
                    let productInfo = {};
                    if (log.type === '替换') {
                        productInfo = {
                            '产品编号': log.oldProductNumber,
                            '替换料号': log.newProductNumber,
                            '产品描述': log.newProductDescription,
                            '生命周期': log.newProductLifecycle,
                        };
                    } else {
                         productInfo = {
                            '产品编号': log.product.partNumber,
                            '替换料号': '',
                            '产品描述': log.product.description,
                            '生命周期': log.product.lifecycle,
                        };
                    }

                    return {
                        '模型编号': solution ? solution.id : 'N/A',
                        '模型描述': solution ? solution.name : 'N/A',
                        '操作': log.type,
                        ...productInfo,
                        '时间戳': log.timestamp
                    };
                });

                const worksheet = XLSX.utils.json_to_sheet(exportData);
                const workbook = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(workbook, worksheet, '变更记录');
                XLSX.writeFile(workbook, `维护记录_${new Date().toISOString().slice(0,10)}.xlsx`);
                this.showNotification('变更记录已导出', 'success');
            }

            // 导出方案清单
            exportCurrentSolution() {
                if (!this.currentSolution) {
                    this.showNotification('请先选择一个方案', 'warning');
                    return;
                }

                const workbook = XLSX.utils.book_new();
                const level1Nodes = this.currentSolution.hierarchy;

                // 通常层级数据在第一个子节点下
                const actualHierarchy = level1Nodes.length > 0 && level1Nodes[0].children ? level1Nodes[0].children : [];

                actualHierarchy.forEach(level2Node => {
                    // 将级别2的描述作为Sheet名称
                    const sheetName = level2Node.description.replace(/[\*\[\]\:\?\/\\']/g, '').substring(0, 31);
                    const flatData = [];

                    const flatten = (nodes, parentPath) => {
                        nodes.forEach(node => {
                            // 只处理产品（级别4）
                            if (node.level === 4) {
                                const lifecycleInfo = this.data.productLifecycle[node.partNumber] || {};
                                flatData.push({
                                    '一级': parentPath[0],
                                    '二级': parentPath[1],
                                    '三级': parentPath[2],
                                    '产品描述': lifecycleInfo.description || node.description,
                                    '产品编号': node.partNumber,
                                    '数量': node.quantity,
                                    '生命周期': lifecycleInfo.lifecycle || node.lifecycle || '未知',
                                    '备注': node.note,
                                });
                            }
                            if (node.children && node.children.length > 0) {
                                flatten(node.children, [...parentPath, node.description]);
                            }
                        });
                    };

                    // 从级别3开始遍历
                    if(level2Node.children) {
                        flatten(level2Node.children, [this.currentSolution.hierarchy[0].description, level2Node.description]);
                    }

                    if (flatData.length > 0) {
                        const worksheet = XLSX.utils.json_to_sheet(flatData);
                        XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
                    }
                });

                if (workbook.SheetNames.length === 0) {
                    this.showNotification('当前方案没有可导出的产品数据', 'warning');
                    return;
                }

                XLSX.writeFile(workbook, `${this.currentSolution.id}_方案清单.xlsx`);
                this.showNotification('方案清单已导出', 'success');
            }

            // 显示通知
            showNotification(message, type = 'info') {
                const colors = {
                    info: 'bg-blue-500',
                    success: 'bg-green-500',
                    warning: 'bg-yellow-500',
                    error: 'bg-red-500',
                };
                const icon = {
                    info: 'fa-info-circle',
                    success: 'fa-check-circle',
                    warning: 'fa-exclamation-triangle',
                    error: 'fa-times-circle',
                }

                const notification = document.createElement('div');
                notification.className = `fixed bottom-5 right-5 text-white px-4 py-3 rounded-lg shadow-xl flex items-center transition-all duration-300 transform translate-x-full ${colors[type]}`;

                notification.innerHTML = `
                    <i class="fas ${icon[type]} mr-3 text-lg"></i>
                    <span>${message}</span>
                    <button class="ml-4 text-xl font-semibold">&times;</button>
                `;

                notification.querySelector('button').onclick = () => {
                    notification.style.transform = 'translateX(120%)';
                    setTimeout(() => notification.remove(), 300);
                };

                document.body.appendChild(notification);

                // Slide in animation
                setTimeout(() => {
                    notification.style.transform = 'translateX(0)';
                }, 10);

                // Auto-dismiss
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        notification.style.transform = 'translateX(120%)';
                        setTimeout(() => notification.remove(), 300);
                    }
                }, 4000);
            }
        }

        // 初始化应用（兼容 DOMContentLoaded 可能已触发的情况）
        (function initApp() {
            const start = () => {
                console.log("初始化应用：创建 SolutionManagerApp 实例");
                try {
                    window.app = new SolutionManagerApp();
                    console.log("SolutionManagerApp 实例已创建，app:", window.app);

                    // 验证关键元素是否存在
                    const fileInput = document.getElementById('fileInput');
                    const fileDropArea = document.getElementById('fileDropArea');
                    const chooseBtn = document.getElementById('chooseFileBtn');
                    console.log('关键元素检查:', {
                        fileInput: !!fileInput,
                        fileDropArea: !!fileDropArea,
                        chooseBtn: !!chooseBtn,
                        XLSX: typeof XLSX
                    });
                } catch (error) {
                    console.error("初始化失败:", error);
                }
            };
            if (document.readyState === 'loading') {
                console.log("等待 DOMContentLoaded");
                document.addEventListener('DOMContentLoaded', start, { once: true });
            } else {
                console.log("DOM 已就绪，直接初始化");
                start();
            }
        })();
