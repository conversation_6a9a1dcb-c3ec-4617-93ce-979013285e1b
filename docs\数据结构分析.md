

# 需求：

1. `Mxxx.xlsx` - 层级化解决方案  这个是初始化数据源。

   - 一个文件代表一个方案，系统需要在一开始进行初始方案数据导入。展示编号及名称，如下：

   | 编号          | 部件类型      | 描述/名称                                             |
   | ------------- | ------------- | ----------------------------------------------------- |
   | M200701.00024 | M产品数据模型 | 司法SDT(司法行政)【主推】智慧监狱安防智能指挥解决方案 |

   - 针对层级关系，需要合理分配便于调阅，级别2可以作为页签展示，下面级别可以在正文中以分类显示展示，核心展示内容是级别4，此处的选件编号即产品编号，特征是1.开头的 不是X开头的。  也就是说级别4是按产品显示详细字段，其他都是分组标识
   优化SolutionViewer组件，将级别2作为页签展示
  在每个页签内，以分类方式展示级别3和级别4
  突出显示级别4（产品）的详细信息
   - 需要导入的产品字段：物件编号、物件描述、备注

2. 政府退市查询xxx.xlsx- 扁平化产品关联列表  这个是会经常刷新导入的数据源，数据有冲突遗漏的以刷新导入的这个表为准。

   - 根据模型编号匹配对应方案
   - 根据选件编号匹配层级
   - 产品编号主要做产品刷新，等同于上述4层的选件编号，展示以产品编号展示
   - 产品描述等同于上述物件描述。展示以产品描述展示。
   - 生命周期需要增加展示。

3. 初始化导入会建立方案（核心是建立目录层级），产品选项刷新会做刷新导入，然后选择方案后可以做分层产品选型展示，可以做产品数据维护，可以做刷新记录导出、完整方案清单导出。

   - 产品数据维护：选中产品物料可以点替换/删除，也可以选插入（向上/向下）新产品物料。额外需支持（针对所有导入的方案）筛选不同生命周期的产品并按产品物料名称排序展示，展示模式可以参考下面这样。然后选中产品编号可以选替换/删除，替换就是换料号就行别的不动，然后替换支持批量替换该物料（针对该物料多次出现的别的地方也换成这个物料）

     | 模型编号      | 模型描述                                  | 模型产品线        | 选件编号     | 选件描述          | 产品编号        | 产品描述                                      | 生命周期 |
     | ------------- | ----------------------------------------- | ----------------- | ------------ | ----------------- | --------------- | --------------------------------------------- | -------- |
     | M200701.00025 | 司法SDT（海关）综合保税区智能监管解决方案 | 政府解决方案_司法 | X08.01.01306 | 增值模块-智能应用 | 2.9.01.01.10080 | 智能目标布控                                  | 量产     |
     | M200701.00025 | 司法SDT（海关）综合保税区智能监管解决方案 | 政府解决方案_司法 | X08.01.01204 | 装卸区            | 1.0.01.23.14532 | 国内大华智慧物流专用分析一体机DH-IVSS716WL-8M | 工程样机 |

   - 维护记录导出：需要记录上次导出时间，从上次导出后到这次导出，出现的维护（增删替换），支持批量导出excel，导出格式如下：

     | 模型编号      | 模型描述                                              | 模型产品线        | 选件编号     | 选件描述           | 产品编号        | 操作 | 替换料号        | 产品描述                                      | 生命周期 |
     | ------------- | ----------------------------------------------------- | ----------------- | ------------ | ------------------ | --------------- | ---- | --------------- | --------------------------------------------- | -------- |
     | M200701.00025 | 司法SDT（海关）综合保税区智能监管解决方案             | 政府解决方案_司法 | X08.01.01306 | 增值模块-智能应用  | 2.9.01.01.10080 | 新增 |                 | 智能目标布控                                  | 量产     |
     | M200701.00025 | 司法SDT（海关）综合保税区智能监管解决方案             | 政府解决方案_司法 | X08.01.01204 | 装卸区             | 1.0.01.23.14532 | 删除 |                 | 国内大华智慧物流专用分析一体机DH-IVSS716WL-8M | 工程样机 |
     | M200701.00024 | 司法SDT(司法行政)【主推】智慧监狱安防智能指挥解决方案 | 政府解决方案_司法 | X08.01.00910 | 高性能融合通信网关 | 1.0.01.08.10039 | 替换 | 1.0.01.08.10041 | 国内大华融合调度UC80系列接口板DH-UC80-MIF02DA | 量产     |
     | M200701.00025 | 司法SDT（海关）综合保税区智能监管解决方案             | 政府解决方案_司法 | X08.01.01307 | 增值模块-安防应用  | 2.9.01.02.10219 | 新增 |                 | 工单管理系统                                  | 量产     |

   - 方案清单excel导出：一级的高配、低配作为sheet，二层子类，三层子场景，四层 产品描述，包含上面说的导入的所有字段。



# 数据结构

## 1. `Mxxx.xlsx` - 层级化解决方案物料清单

该文件是一个复杂的、包含多个信息块的层级化BOM（Bill of Materials）清单。

### 1.1 文件结构概览

文件内容可分为三个主要部分：

1. **文件头信息 (Header)**: 包含创建者、部门等元数据。
2. **方案元数据 (Metadata)**: 包含方案的业务属性、版本、项目编号等信息。
3. **BOM清单**: 文件的核心，定义了解决方案的层级结构和具体物料。

### 1.2 BOM清单数据结构

BOM清单是核心，其数据结构具有明显的层级关系。

#### 关键字段定义:

* `级别` (Integer): 定义了当前行在层级结构中的深度，是判断父子关系的核心依据。
* `物件编号` (String): 节点的唯一标识符。
  * **分类节点**: 以 `X08.` 开头，如 `X08.01.01002`。这类节点代表方案中的一个分类或功能模块（例如，“视频监控系统”），通常是“虚拟件”。
  * **实体物料**: 以数字开头，如 `1.0.01.04.39733`。这类节点代表具体的物理设备或软件产品。
* `物件描述` (String): `物件编号` 对应的可读名称。
* `物件类型` (String): 节点的类型，如 `X产品数据选件` (分类节点) 或 `10成品` (实体物料)。
* `数量` (Integer): 该物料在上一层级中的数量。
* `生命周期阶段` (String): 物料的当前状态，如 `开发`、`量产`。



我将`Mxxx.xlsx`文件中BOM清单部分的主要列分为以下几个类别，以便于理解：

### 1. 核心标识与层级信息

这些是定义每个节点身份和层级关系的基础列。

* `级别`: **(核心)** 数字，代表当前行在树状结构中的深度。
* `物件编号`: **(核心)** 节点的唯一ID。以`X08`开头的是分类节点，数字开头的是具体产品/物料。
* `物件描述`: **(核心)** 节点的可读名称，是我们界面上显示的主要内容。
* `物件版本`: 节点的版本号。
* `查找编号`: BOM行项目的一个顺序编号，通常是10, 20, 30...这样递增。

### 2. 配置与用量信息

这些列定义了物料在方案中是如何被使用的。

* `数量`: 该物料在上一级节点中被使用的数量。
* `位置号`: 用于在电路板或装配图中标示组件位置的编号。
* `主料/替代料`: 标记该物料是主要用料还是备选的替代料。
* `可选`: 是否为可选组件。
* `互不相容`: 标记不能与其他特定组件同时使用的规则。
* `是否主推`: 是否为方案中推荐使用的物料。

### 3. 状态与分类信息

这些列描述了物料自身的属性和当前所处阶段。

* `物件类型`: 例如 `X产品数据选件` (分类)、`10成品` (最终产品)、`1201监控器材` (物料分类)。
* `物件的生命周期阶段`: 例如 `开发`、`量产`、`即将停售`、`废弃`。
* `器件认定`: 器件的认证状态，如 `已认定`、`未认定`。
* `物料优选状态`: 标记物料的推荐等级。

### 4. 制造商信息

当物料是外购件时，这些列提供了供应商信息。

* `制造商名称`: 生产该部件的公司名称。
* `制造商部件号`: 制造商为该部件指定的型号或编号。
* `制造商部件描述`: 制造商提供的部件描述。

### 5. 技术与备注信息

提供额外的技术细节和人工备注。

* `备注`: 人工填写的任意补充说明信息。
* `烧录方式`: 如果是需要烧录程序的芯片，这里会说明烧录方法。
* `CAD文件名`: 相关的CAD设计文件名。
* `英文描述`: 物料的英文名称。

### 6. 系统状态标志

这些通常是导出系统内部使用的布尔值标志，在前端展示时可能不是必需的。

* `附件（图像）`
* `制造商（图像）`
* `等待变更（图像）`
* `已标记（图像）`
* `具有质量（图像）`

这份详细的列分析能帮助我们更全面地理解每一条数据的含义。

#### 层级关系示例:

```mermaid
graph TD
    A["级别 1 (X08.01.01002 - 高配)"] --> B["级别 2 (X08.01.00819 - 视频监控)"];
    B --> C["级别 3 (X08.01.00843 - 监室)"];
    C --> D["级别 4 (1.0.01.04.39733 - 摄像机)"];
    C --> E["级别 4 (1.2.01.33.10019 - 拾音器)"];
```

***

## 2. `政府退市查询xxx.xlsx` - 扁平化产品关联列表

该文件是一个扁平化的表格，核心作用是建立 **解决方案模型**、**选件** 和 **具体产品** 之间的直接关联关系。

### 2.1 文件结构概览

这是一个简单的、非层级的表格，主要用于数据查询和报告。

### 2.2 数据结构定义

* `模型编号` (String): 顶级解决方案的ID，如 `M200701.00025`。
* `模型描述` (String): 解决方案的名称。
* `选件编号` (String): 解决方案下的分类节点ID，与 `Mxxx.xlsx` 中的 `物件编号` (X08.\*) 对应。
* `选件描述` (String): 分类节点的名称。
* `产品编号` (String): 最终产品的ID，与 `Mxxx.xlsx` 中的 `物件编号` (数字串) 对应。
* `产品描述` (String): 产品的名称。
* `生命周期` (String): 产品的状态，如 `量产`、`即将停售`。

### 2.3 数据关系图

这个文件揭示了两个核心数据实体之间的多对多关系。

```mermaid
erDiagram
    SOLUTION_MODEL {
        string model_id PK
        string description
        string product_line
    }
    PRODUCT {
        string product_id PK
        string description
        string lifecycle_status
    }
    OPTION {
        string option_id PK
        string description
    }

    SOLUTION_MODEL ||--o{ OPTION : "contains"
    OPTION ||--o{ PRODUCT : "contains"
```

**解读**:

* 一个 **解决方案模型 (SOLUTION\_MODEL)** 包含多个 **选件 (OPTION)**。
* 一个 **选件 (OPTION)** 包含多个 **具体产品 (PRODUCT)**。
* 这个表格的每一行，都明确定义了一条从 `模型 -> 选件 -> 产品` 的完整路径。

***

## 3. 总结与关联

* `Mxxx.xlsx` 定义了 **“是什么”** 和 **“如何构成”**。它描述了一个解决方案内部的完整、详细的层级结构。
* `政府退市查询xxx.xlsx` 则提供了一个 **“快速查询索引”**。它把层级结构扁平化，让我们能迅速知道某个具体产品属于哪个解决方案的哪个部分。

这两个文件结合在一起，为我们提供了理解整个解决方案清单所需的全貌。
