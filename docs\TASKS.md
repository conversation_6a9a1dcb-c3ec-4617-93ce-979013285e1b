# 项目任务清单与架构设计

本文档详细定义了“解决方案清单管理工具”的开发任务、技术选型和基本架构。


## 2. 核心功能模块分解 (Feature Breakdown)

---

### **Phase 1: 核心数据处理与展示**

#### **任务2: 数据导入与解析**
- [ ] **2.1 文件上传组件**: 创建一个允许用户通过点击或拖拽上传 `Mxxx.xlsx` 和 `政府退市查询xxx.xlsx` 文件的UI组件。
- [ ] **2.2 `Mxxx.xlsx` 解析**:
    - [ ] 读取Excel文件。
    - [ ] 识别并提取BOM清单部分的数据。
    - [ ] 根据“级别”列，将扁平数据精确地转换为树状（层级化）数据结构。
    - [ ] 每个节点需包含关键信息：`物件编号`, `物件描述`, `备注`, `生命周期阶段`等。
- [ ] **2.3 `政府退市查询xxx.xlsx` 解析**:
    - [ ] 读取Excel文件。
    - [ ] 将其内容解析为一个易于查询的Map或对象，键可以是“产品编号”，值为包含“生命周期”等信息的对象。

#### **任务3: 核心UI实现 - 方案展示**
- [ ] **3.1 主布局**:
    - [ ] 设计一个两栏布局：左侧为已导入的方案列表，右侧为选中方案的详细内容。
- [ ] **3.2 方案列表**:
    - [ ] 每当一个 `Mxxx.xlsx` 文件被成功导入，就在左侧列表中显示其方案名称和编号。
    - [ ] 用户可以点击列表中的方案进行切换。
- [ ] **3.3 层级化内容展示**:
    - [ ] **二级分类 (页签)**: 将级别2的节点作为顶部的页签（Tabs）展示。
    - [ ] **三级及以下分类 (折叠面板)**: 在每个页签内部，使用折叠面板（Accordion）或树状视图来展示三级和四级内容。
    - [ ] **四级产品 (核心内容)**: 清晰地表格化展示级别4的产品信息，这是核心展示内容。

---

### **Phase 2: 数据交互与维护**

#### **任务4: 数据刷新与整合**
- [ ] **4.1 数据更新**: 当 `政府退市查询xxx.xlsx` 文件被导入时，遍历现有方案数据，使用该文件中的信息（特别是“生命周期”）更新匹配的“产品编号”的记录。
- [ ] **4.2 冲突处理**: 明确规则：以 `政府退市查询xxx.xlsx` 的数据为准。

#### **任务5: 产品数据维护功能**
- [ ] **5.1 交互实现**:
    - [ ] 用户可以选中任意一个产品行（级别4）。
    - [ ] 选中后，出现“替换”、“删除”、“向上插入”、“向下插入”按钮。
- [ ] **5.2 功能逻辑**:
    - [ ] **替换**: 弹出一个对话框，让用户输入新的“产品编号”，更新当前行数据。支持“批量替换”选项，若勾选，则替换所有方案中出现的相同旧料号。
    - [ ] **删除**: 从数据结构中移除该产品节点。
    - [ ] **插入**: 弹出一个对话框，让用户查找并选择一个新产品，然后将其插入到当前产品的上方或下方。
- [ ] **5.3 变更追踪**: 所有增、删、改操作都需要被记录下来，为“维护记录导出”做准备。

---

### **Phase 3: 导出与完成**

#### **任务6: 产品筛选与排序**
- [ ] **6.1 筛选**: 提供一个筛选器，允许用户按“生命周期”状态（如“量产”、“停产”）筛选并展示所有方案中的产品。
- [ ] **6.2 排序**: 筛选结果可以按“产品描述”进行字母排序。

#### **任务7: 导出功能**
- [ ] **7.1 维护记录导出**:
    - [ ] 提供一个“导出维护记录”按钮。
    - [ ] 将所有被追踪的变更，按照 `数据结构分析.md` 中定义的格式生成并下载为Excel文件。
- [ ] **7.2 完整方案清单导出**:
    - [ ] 提供一个“导出方案”按钮。
    - [ ] 将当前选中的、经过维护的完整方案，按照 `数据结构分析.md` 中定义的多Sheet格式生成并下载为Excel文件。

#### **任务8 & 9: UI优化与构建**
- [ ] **8.1 UI/UX 优化**: 整体界面美化，确保交互流畅、提示清晰。
- [ ] **9.1 构建**: 配置Vite，将整个Vue应用打包成一个独立的、无外部依赖的 `index.html` 文件。

## 3. 应用架构图 (Application Architecture)

```mermaid
graph TD
    subgraph "用户浏览器"
        A[index.html] --> B{Vue.js App};

        subgraph "Vue.js App"
            C[UI Components] -- 用户操作 --> D[State Management];
            D -- 更新数据 --> C;
            D -- 调用逻辑 --> E;
        end

        subgraph "核心逻辑"
            E[Services/Utils] -- 解析/生成 --> F[SheetJS Lib];
        end

        B --> C;
        B --> D;
        B --> E;
    end

    G[用户] -- 上传Excel文件 --> A;
    A -- 下载Excel文件 --> G;
